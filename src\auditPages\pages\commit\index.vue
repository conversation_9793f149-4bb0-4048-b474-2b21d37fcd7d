<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnFormItem from '@tuniao/tnui-vue3-uniapp/components/form/src/form-item.vue'
import TnForm from '@tuniao/tnui-vue3-uniapp/components/form/src/form.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import TnPicker from '@tuniao/tnui-vue3-uniapp/components/picker/src/picker.vue'
import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue'
import Acknowledge from '@/components/Acknowledge.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { COMMIT_RULES } from '@/pages/tabbarPages/commit'
import { levelOptions, majorOptions } from '@/pages/tabbarPages/content'
import { jumpPageTo } from '@/utils/pageTo'

const isGet = ref(false)
const isBottom = ref(false)
// const url = ref(`/static/pdfjs/web/viewer.html?file=${encodeURIComponent('https://www.keylitos.com/pdf/producta.pdf')}`)

// 境外考生进入页面首先进行信息完善
const showPopup = ref(true)
// 应聘专业
const openMajorPicker = ref(false)
// 学历层次
const openLevelPicker = ref(false)

const formRef = ref()

// 预览显示
// const showWeb = ref(false)
// 文档路径
// const url = ref('https://www.keylitos.com/pdf/producta.pdf')

// 境外需填数据
const formData = ref({
  major: 0,
  level: 0,
  schoolSystem: 1,
})

function submitInfo() {
  formRef.value?.validate((valid) => {
    if (valid) {
      showPopup.value = false
      uni.showToast({
        title: '提交成功',
      })
      // wx.openDocument({
      //   filePath: 'https://www.keylitos.com/pdf/producta.pdf',
      // })
    }
    else {
      uni.showToast({
        title: '表单校验失败',
        icon: 'none',
      })
    }
  })
}

// 下载浏览文件
function downloadFile() {
  uni.downloadFile({
    url: 'https://www.keylitos.com/pdf/producta.pdf',
    success(res) {
      const filePath = res.tempFilePath
      uni.openDocument({
        filePath,
        success() {
          console.log('打开PDF成功')
        },
        fail(err) {
          console.log('打开PDF失败', err)
        },
      })
    },
    fail(err) {
      console.log('下载PDF失败', err)
    },
  })
}

// 触底检测
function scrolltolower() {
  isBottom.value = true
  // console.log(isBottom.value)
}

// 跳转至签字页面
function studKnowClick() {
  jumpPageTo({ url: '/auditPages/pages/signaturePads/index' })
}

// 当内容不需要滚动时
onMounted(() => {
  const query = uni.createSelectorQuery()
  query.select('.scroll-view-container').boundingClientRect((data) => {
    query.select('.scroll-view-content').boundingClientRect((res) => {
      if (res && data && res.height <= data.height && !isGet.value) {
        isGet.value = true
        isBottom.value = true
      }
    }).exec()
  }).exec()
})
</script>

<template>
  <SafeTopArea>
    <List>
      <view class="font-[PingFangSC]">
        <view class="header relative top-[50rpx] h-[80rpx] w-full bg-white">
          <Header title="考生承诺书" type="black" right="download" @rightClick="downloadFile" />
        </view>
        <SafeArea :header="true">
          <scroll-view
            scroll-y class="scroll-view-container m-auto h-full bg-white p-[44rpx] pb-[150rpx]"
            @scrolltolower="scrolltolower"
          >
            <view class="scroll-view-content">
              <text class="text-[34rpx] font-[400] leading-[60rpx]" />
              <!-- <web-view :src="url" /> -->
            </view>
          </scroll-view>
        </SafeArea>
        <Acknowledge :isBottom="isBottom" title="考生承诺书" @studKnow="studKnowClick" />
      </view>
    </List>
    <TnPopup v-model="showPopup" width="90%" :overlay-closeable="false">
      <view class="flex flex-col gap-[40rpx] p-[40rpx]">
        <view class="text-center text-[40rpx] font-[500]">
          请先完善您的信息
        </view>
        <TnForm
          ref="formRef" :model="formData" :rules="COMMIT_RULES" :hide-required-asterisk="true"
          label-width="150rpx"
        >
          <TnFormItem label="应聘专业" prop="major">
            <TnInput
              v-model="majorOptions[formData.major ?? 0].label" type="select" placeholder="请选择" underline
              @click="openMajorPicker = true"
            >
              <template #suffix>
                <TnIcon name="right" />
              </template>
            </TnInput>
          </TnFormItem>
          <TnFormItem label="学历层次" prop="level">
            <TnInput
              v-model="levelOptions[formData.level ?? 0].label" type="select" placeholder="请选择" underline
              @click="openLevelPicker = true"
            >
              <template #suffix>
                <TnIcon name="right" />
              </template>
            </TnInput>
          </TnFormItem>
          <TnFormItem label="学制" prop="schoolSystem">
            <TnInput v-model="formData.schoolSystem" type="number" placeholder="请输入" underline />
          </TnFormItem>
        </TnForm>
        <view class="flex-center">
          <TnButton plain text-color="#3e89fa" :text="true" @click="submitInfo">
            确认
          </TnButton>
        </view>
      </view>
    </TnPopup>
    <!-- 应聘专业选择 -->
    <TnPicker v-model="formData.major" v-model:open="openMajorPicker" :data="majorOptions" />
    <!-- 学历层次选择 -->
    <TnPicker v-model="formData.level" v-model:open="openLevelPicker" :data="levelOptions" />
  </SafeTopArea>
</template>

<style scoped lang="scss">
.header {
  box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(200, 199, 199, 0.32);
}
</style>
